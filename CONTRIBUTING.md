# Contribute

If you would like to contribute, take a look at "Issues". Feel free to open a new issue if the existing ones are not suitable.

In future, it is planned to create interfaces to popular streaming services such as Spotify or Apple Music. After that, it would be nice to somehow summarize the results from all interfaces.

Please note that there are no unit tests so far, so at this point one has to test all the features by hand.

## How to contribute

* Prepare a dedicated branch.
* Do your changes.
* Lint the code using flake8. (I use the python3.5 version.) Codes E501 and F821 can currently be ignored.
* Commit everything and create a pull request.
